﻿// 定义导航函数
function navigateInIframe(url, title) {
    try {
        // 获取当前iframe索引
        var currentFrame = window.frameElement;
        var parentDocument = window.parent.document;

        // 如果是在tab中，尝试更新tab的标题和URL
        if (window.parent.layui) {
            var tabTitle = parentDocument.querySelector('.layui-tab-title');
            if (tabTitle) {
                var activeLi = tabTitle.querySelector('.layui-this');
                if (activeLi) {
                    // 更新标题
                    activeLi.innerText = title;
                    // 更新URL属性
                    activeLi.setAttribute('lay-attr', url);

                    // 更新iframe的src
                    var iframeId = activeLi.getAttribute('lay-id');
                    if (iframeId) {
                        var targetIframe = parentDocument.querySelector('iframe[data-id="' + iframeId + '"]');
                        if (targetIframe) {
                            targetIframe.src = url;
                            return;
                        }
                    }
                }
            }
        }

        // 如果上面的方法都失败了，直接修改当前iframe的src
        currentFrame.src = url;

    } catch (e) {
        console.error('Navigation error:', e);
        // 最后的备选方案：直接跳转
        window.location.href = url;
    }
}

// 在新的iframe标签页中打开链接
function navigateInNewTab(url, title) {
    console.log('navigateInNewTab 被调用:', url, title);

    try {
        console.log('检查父窗口结构...');
        console.log('window.parent:', window.parent);
        console.log('window.parent.layui:', window.parent.layui);

        // 检查父窗口是否有layui
        if (window.parent.layui && window.parent.layui.element) {
            console.log('找到 layui.element');
            var element = window.parent.layui.element;

            // 检查是否存在标签页容器
            var tabContainer = window.parent.document.querySelector('.layui-tab[lay-filter="layuiminiTab"]');
            if (!tabContainer) {
                tabContainer = window.parent.document.querySelector('.layui-tab');
                console.log('未找到layuiminiTab，尝试查找任意标签页容器:', tabContainer);
            }

            if (tabContainer) {
                console.log('找到标签页容器，尝试添加新标签页...');

                // 生成唯一的标签页ID
                var tabId = 'tab_' + Date.now();

                // 获取标签页过滤器名称
                var filterName = tabContainer.getAttribute('lay-filter') || 'layuiminiTab';
                console.log('使用过滤器名称:', filterName);

                // 确保URL使用FriendlyUrls格式
                var cleanUrl = url;
                if (cleanUrl.indexOf('.aspx') > -1) {
                    cleanUrl = cleanUrl.replace('.aspx', '');
                    console.log('转换为FriendlyUrls格式:', cleanUrl);
                }

                // 添加新的标签页
                element.tabAdd(filterName, {
                    title: '<span class="layuimini-tab-active"></span><span>' + (title || '新标签页') + '</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>',
                    content: '<iframe src="' + cleanUrl + '" frameborder="0" style="width:100%;height:100%;" data-url="' + cleanUrl + '" data-id="' + tabId + '"></iframe>',
                    id: tabId
                });

                // 保存标签页信息到sessionStorage
                window.parent.sessionStorage.setItem('layuiminimenu_' + tabId, title || '新标签页');

                // 切换到新标签页
                element.tabChange(filterName, tabId);

                console.log('标签页添加成功，ID:', tabId, 'URL:', cleanUrl);
                return true;
            } else {
                console.warn('未找到标签页容器');
            }
        } else {
            console.warn('未找到 layui.element');
        }
        
        // 尝试其他方法
        console.log('尝试其他备选方案...');
        
        // 检查是否有自定义的添加标签页函数
        if (window.parent.addTab && typeof window.parent.addTab === 'function') {
            console.log('找到自定义 addTab 函数');
            window.parent.addTab(url, title);
            return true;
        }
        
        // 检查是否有其他可能的标签页管理函数
        if (window.parent.openNewTab && typeof window.parent.openNewTab === 'function') {
            console.log('找到 openNewTab 函数');
            window.parent.openNewTab(url, title);
            return true;
        }
        
        // 最后的备选方案：在新窗口中打开
        console.log('使用新窗口打开作为备选方案');
        window.open(url, '_blank');
        return true;
        
    } catch (e) {
        console.error('打开新标签页失败:', e);
        
        // 错误时的备选方案：在新窗口中打开
        try {
            console.log('错误处理：使用新窗口打开');
            window.open(url, '_blank');
            return true;
        } catch (openError) {
            console.error('打开新窗口也失败:', openError);
            alert('无法打开新页面，请检查浏览器设置');
            return false;
        }
    }
}

// 添加一个用于调试的函数，可以帮助我们了解当前iframe的具体环境
function debugIframeInfo() {
    console.log('Current window location:', window.location.href);
    console.log('Frame element:', window.frameElement);
    if (window.frameElement) {
        console.log('Frame ID:', window.frameElement.id);
        console.log('Frame name:', window.frameElement.name);
        console.log('Frame attributes:', window.frameElement.attributes);
    }
    console.log('Parent layui available:', !!window.parent.layui);
    console.log('Parent element available:', !!window.parent.element);
}