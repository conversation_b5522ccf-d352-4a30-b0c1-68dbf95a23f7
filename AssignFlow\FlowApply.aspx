<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="FlowApply.aspx.cs" Inherits="WebApplication1.AssignFlow.FlowApply" %>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>报价单审批申请</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        body {
            background-color: #f5f7fa;
        }

        /* 卡片容器样式 */
        .bom-container {
            max-height: 80vh;
            overflow-y: auto;
            padding: 10px;
        }

        .bom-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .bom-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        /* 卡片头部 */
        .bom-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            position: relative;
        }

        .bom-card-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .bom-item-number {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }

        /* 卡片内容 */
        .bom-card-content {
            padding: 0;
        }

        /* 价格区域 */
        .price-section {
            background: #f8f9ff;
            border-bottom: 1px solid #e8e8e8;
            padding: 20px;
        }

        .current-price-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .price-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .current-price {
            font-size: 24px;
            font-weight: 700;
            color: #1890ff;
        }

        .lowest-price-badge {
            background: linear-gradient(45deg, #52c41a, #73d13d);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .expand-prices-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .expand-prices-btn:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        /* 价格选项区域 */
        .price-options {
            display: none;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e8e8e8;
        }

        .price-options.show {
            display: block;
        }

        .price-options-title {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            margin-bottom: 12px;
        }

        .price-option {
            background: white;
            border: 2px solid #e8e8e8;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .price-option:hover {
            border-color: #40a9ff;
            background: #f6f8ff;
        }

        .price-option.selected {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        .price-option-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .price-option-price {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
        }

        .price-option-details {
            font-size: 12px;
            color: #666;
        }

        .price-option input[type="radio"] {
            margin: 0;
            transform: scale(1.2);
        }

        /* 详细信息区域 */
        .details-section {
            padding: 20px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
        }

        .detail-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        /* 备注区域 */
        .remark-section {
            background: #fafafa;
            padding: 16px 20px;
            border-top: 1px solid #e8e8e8;
        }

        .remark-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .remark-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .remark-input:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .remark-required {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
        }

        /* 按钮样式优化 */
        .submit-btn, .debug-btn {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            gap: 8px !important;
            padding: 12px 32px !important;
            font-size: 16px !important;
            font-weight: 500 !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            border: none !important;
            cursor: pointer !important;
            vertical-align: middle !important;
        }

        .submit-btn {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4) !important;
        }

        .debug-btn {
            background: #f5f5f5 !important;
            color: #666 !important;
            border: 1px solid #d9d9d9 !important;
            margin-left: 16px !important;
            padding: 10px 24px !important;
            font-size: 14px !important;
        }

        .debug-btn:hover {
            background: #e6f7ff !important;
            border-color: #40a9ff !important;
            color: #1890ff !important;
            transform: translateY(-1px) !important;
        }

        .submit-btn i, .debug-btn i {
            font-size: 16px !important;
            line-height: 1 !important;
            margin: 0 !important;
        }

        .submit-btn span, .debug-btn span {
            line-height: 1 !important;
            margin: 0 !important;
            font-weight: inherit !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .details-grid {
                grid-template-columns: 1fr;
            }

            .current-price-display {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .price-option {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .submit-btn, .debug-btn {
                padding: 10px 24px !important;
                font-size: 14px !important;
            }

            .debug-btn {
                margin-left: 8px !important;
                margin-top: 8px !important;
            }
        }
    </style>
</head>
<body>
    <div class="layui-container" style="max-width: 1200px;">
        <div class="layui-row" style="margin-top: 20px;">
            <!-- 页面标题 -->
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333; margin: 0;">报价单审批申请</h2>
                <p style="color: #666; margin: 8px 0 0 0;">请选择合适的价格并填写必要的备注信息</p>
            </div>

            <!-- BOM卡片容器 -->
            <div class="bom-container" id="bomContainer">
                <!-- 动态生成BOM卡片 -->
            </div>

            <!-- 操作按钮 -->
            <div class="layui-form-item" style="text-align: center; margin-top: 20px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <button type="button" class="layui-btn layui-btn-normal layui-btn-lg submit-btn" id="submitApproval">
                    <i class="layui-icon layui-icon-ok"></i>
                    <span>提交审批</span>
                </button>
                <button type="button" class="layui-btn layui-btn-primary debug-btn" id="debugBtn">
                    <i class="layui-icon layui-icon-console"></i>
                    <span>调试信息</span>
                </button>
            </div>
        </div>
    </div>
    <script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
    <script src="../js/navigate.js" charset="utf-8"></script>
    <script>
        layui.use(['form', 'jquery', 'layer'], function () {
            var form = layui.form,
                $ = layui.jquery,
                layer = layui.layer;

            // 页面加载调试信息
            console.log('=== FlowApply页面加载调试信息 ===');
            console.log('当前URL:', window.location.href);
            console.log('查询字符串:', window.location.search);
            console.log('Hash:', window.location.hash);
            console.log('是否在iframe中:', window !== window.parent);
            if (window.frameElement) {
                console.log('iframe元素:', window.frameElement);
                console.log('iframe src:', window.frameElement.src);
            }

            function getQueryParam(name) {
                // 优化参数获取逻辑，增加多种获取方式和错误处理
                var value = null;

                // 方法1：从URL查询字符串获取
                var queryParams = window.location.search.substr(1);
                if (queryParams) {
                    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                    var r = queryParams.match(reg);
                    if (r != null) {
                        value = decodeURIComponent(r[2]);
                        console.log('从查询字符串获取参数 ' + name + ':', value);
                        return value;
                    }
                }

                // 方法2：从hash中获取参数
                var hash = window.location.hash;
                if (hash && hash.indexOf('?') > -1) {
                    var hashParams = hash.substring(hash.indexOf('?') + 1);
                    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                    var r = hashParams.match(reg);
                    if (r != null) {
                        value = decodeURIComponent(r[2]);
                        console.log('从hash获取参数 ' + name + ':', value);
                        return value;
                    }
                }

                // 方法3：从sessionStorage获取（用于页面刷新时保持参数）
                var sessionKey = 'FlowApply_' + name;
                var sessionValue = sessionStorage.getItem(sessionKey);
                if (sessionValue) {
                    console.log('从sessionStorage获取参数 ' + name + ':', sessionValue);
                    return sessionValue;
                }

                // 方法4：尝试从父窗口的URL获取（iframe环境）
                try {
                    if (window.parent && window.parent !== window) {
                        var parentUrl = window.parent.location.href;
                        var urlParams = new URLSearchParams(parentUrl.split('?')[1] || '');
                        if (urlParams.has(name)) {
                            value = urlParams.get(name);
                            console.log('从父窗口URL获取参数 ' + name + ':', value);
                            return value;
                        }
                    }
                } catch (e) {
                    console.warn('无法访问父窗口URL:', e);
                }

                console.warn('未找到参数:', name);
                return null;
            }

            // 从URL获取RFQNo
            var RFQNo = getQueryParam('RFQNo');
            console.log('获取到的RFQNo:', RFQNo);

            // 参数验证和保存
            if (RFQNo) {
                // 保存参数到sessionStorage，用于页面刷新时恢复
                sessionStorage.setItem('FlowApply_RFQNo', RFQNo);
                console.log('RFQNo已保存到sessionStorage');

                // 更新页面标题
                document.title = '报价单审批申请 - ' + RFQNo;

                // 尝试更新父窗口标签页标题
                try {
                    if (window.parent && window.parent.document) {
                        var currentFrame = window.frameElement;
                        if (currentFrame) {
                            var tabId = currentFrame.getAttribute('data-id');
                            if (tabId && window.parent.document.querySelector) {
                                var tabTitle = window.parent.document.querySelector('[lay-id="' + tabId + '"]');
                                if (tabTitle) {
                                    tabTitle.innerHTML = '<span class="layuimini-tab-active"></span><span>签核流程申请 - ' + RFQNo + '</span><i class="layui-icon layui-unselect layui-tab-close">ဆ</i>';
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.warn('无法更新标签页标题:', e);
                }
            } else {
                // 如果没有获取到参数，显示错误信息并提供返回选项
                layer.alert('缺少必要的参数RFQNo，无法加载页面数据。', {
                    title: '参数错误',
                    icon: 2,
                    btn: ['返回上一页', '重新加载'],
                    yes: function(index) {
                        layer.close(index);
                        // 尝试返回上一页
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            // 如果没有历史记录，尝试关闭当前标签页
                            try {
                                if (window.parent && window.parent.layui && window.parent.layui.element) {
                                    // 在layuimini环境中关闭当前标签页
                                    var currentTabId = window.frameElement ? window.frameElement.getAttribute('data-id') : null;
                                    if (currentTabId) {
                                        window.parent.layui.element.tabDelete('layuiminiTab', currentTabId);
                                    }
                                }
                            } catch (e) {
                                console.warn('无法关闭标签页:', e);
                                window.close();
                            }
                        }
                    },
                    btn2: function(index) {
                        layer.close(index);
                        window.location.reload();
                    }
                });
                return; // 停止后续执行
            }

            // 页面初始化时加载BOM数据
            function loadBOMData() {
                layer.load();
                $.ajax({
                    url: '../ashx/QuoteControl.ashx?action=QueryBom&RFQNo=' + RFQNo,
                    method: 'GET',
                    success: function (res) {
                        layer.closeAll('loading');
                        var Response = JSON.parse(res);
                        if (Response.code == 0) {
                            console.log(Response.bomdata);
                            var bomData = Response.bomdata;
                            var container = $('#bomContainer');
                            container.empty();

                            bomData.forEach(function (item, index) {
                                loadQuotationData(item, container, index + 1);
                            });
                        }
                        else {
                            layer.msg(Response.msg);
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('加载BOM表数据失败');
                    }
                });
            }

            // 加载每个BOM项目的报价数据并生成卡片
            function loadQuotationData(bomItem, container, itemNumber) {
                $.ajax({
                    url: '../ashx/QuoteControl.ashx?action=QueryQuoteListById',
                    method: 'GET',
                    data: { bomId: bomItem.Id },
                    success: function (res) {
                        var response = JSON.parse(res);
                        var quotations = response.data;

                        // 安全检查
                        if (!quotations || quotations.length === 0) {
                            console.warn('No quotations found for BOM item:', bomItem);
                            return;
                        }

                        // 按价格升序排序
                        quotations.sort((a, b) => (a.QUO_RMBPRICE || 0) - (b.QUO_RMBPRICE || 0));

                        var initialQuote = quotations[0];
                        var initialPrice = initialQuote ? initialQuote.QUO_PRICE.toFixed(2) : '0.00';
                        var hasMultipleOptions = quotations.length > 1;
                        var lowestPrice = quotations[0].QUO_RMBPRICE || 0;

                        // 生成BOM卡片
                        generateBOMCard(bomItem, quotations, initialQuote, hasMultipleOptions, lowestPrice, container, itemNumber);
                    },
                    error: function () {
                        layer.msg('加载报价数据失败');
                    }
                });
            }

            // 生成BOM卡片
            function generateBOMCard(bomItem, quotations, initialQuote, hasMultipleOptions, lowestPrice, container, itemNumber) {
                var isLowestPrice = (initialQuote.QUO_RMBPRICE || 0) === lowestPrice;

                var cardHtml = `
                <div class="bom-card" data-bom-id="${bomItem.Id}" data-lowest-price="${lowestPrice}" data-selected-quo-id="${initialQuote.QUO_ID}">
                    <!-- 卡片头部 -->
                    <div class="bom-card-header">
                        <div class="bom-card-title">
                            <span>${bomItem.CUST_PARTNUMBER || '客户料号'}</span>
                            <span class="bom-item-number">项目 ${itemNumber}</span>
                        </div>
                        <div style="font-size: 14px; margin-top: 8px; opacity: 0.9;">
                            ${bomItem.BOM_DESC || '物料描述'}
                        </div>
                    </div>

                    <!-- 卡片内容 -->
                    <div class="bom-card-content">
                        <!-- 价格区域 -->
                        <div class="price-section">
                            <div class="current-price-display">
                                <div class="price-info">
                                    <span class="current-price">￥${(initialQuote.QUO_PRICE || 0).toFixed(2)}</span>
                                    ${isLowestPrice ? '<span class="lowest-price-badge">最低价</span>' : ''}
                                </div>
                                ${hasMultipleOptions ? '<button class="expand-prices-btn" data-bom-id="' + bomItem.Id + '">查看其他价格 (' + quotations.length + ')</button>' : ''}
                            </div>

                            ${hasMultipleOptions ? generatePriceOptions(bomItem.Id, quotations, initialQuote.QUO_ID, lowestPrice) : ''}
                        </div>

                        <!-- 详细信息区域 -->
                        <div class="details-section">
                            <div class="details-grid">
                                <div class="detail-item">
                                    <span class="detail-label">制造商 (MFR)</span>
                                    <span class="detail-value mfr-value">${initialQuote.QUO_MFR || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">制造商料号 (MPN)</span>
                                    <span class="detail-value mpn-value">${initialQuote.QUO_MPN || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">替代料号</span>
                                    <span class="detail-value alter-mpn-value">${initialQuote.ALTER_MPN || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">供应商</span>
                                    <span class="detail-value supplier-value">${initialQuote.QUO_SUPP || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">报价币别</span>
                                    <span class="detail-value currency-value">${initialQuote.QUO_CUR || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">US单价</span>
                                    <span class="detail-value usprice-value">$${(initialQuote.QUO_USPRICE || 0).toFixed(2)}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">SPQ</span>
                                    <span class="detail-value spq-value">${initialQuote.SPQ || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">MOQ</span>
                                    <span class="detail-value moq-value">${initialQuote.MOQ || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">交期 (LT)</span>
                                    <span class="detail-value lt-value">${initialQuote.LT || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">工具费</span>
                                    <span class="detail-value tool-cost-value">${initialQuote.TOOL_COST || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">工具费币别</span>
                                    <span class="detail-value tool-curr-value">${initialQuote.TOOL_CURR || '-'}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">报价日期</span>
                                    <span class="detail-value date-value">${initialQuote.CREATE_DATE || '-'}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 备注区域 -->
                        <div class="remark-section">
                            <div class="remark-label">备注 (Sourcing)</div>
                            <input type="text" class="remark-input" name="remark_${bomItem.Id}"
                                   placeholder="选择非最低价时必须填写备注">
                            <div class="remark-required" style="display: none;">选择非最低价时必须填写备注</div>
                        </div>
                    </div>
                </div>`;

                var $card = $(cardHtml);
                container.append($card);

                // 绑定事件
                bindCardEvents(bomItem.Id, quotations, lowestPrice);
            }

            // 生成价格选项HTML
            function generatePriceOptions(bomId, quotations, selectedQuoId, lowestPrice) {
                var optionsHtml = '<div class="price-options" id="price-options-' + bomId + '">';
                optionsHtml += '<div class="price-options-title">选择价格选项：</div>';

                quotations.forEach(function(quote) {
                    var isLowest = (quote.QUO_RMBPRICE || 0) === lowestPrice;
                    var isSelected = quote.QUO_ID === selectedQuoId;

                    optionsHtml += `
                    <div class="price-option ${isSelected ? 'selected' : ''}" data-quo-id="${quote.QUO_ID}" data-rmb-price="${quote.QUO_RMBPRICE || 0}">
                        <div class="price-option-left">
                            <input type="radio" name="price_${bomId}" value="${quote.QUO_ID}"
                                   data-rmb-price="${quote.QUO_RMBPRICE || 0}" ${isSelected ? 'checked' : ''}>
                            <div>
                                <div class="price-option-price">￥${(quote.QUO_PRICE || 0).toFixed(2)} ${isLowest ? '<span class="lowest-price-badge">最低价</span>' : ''}</div>
                                <div class="price-option-details">${quote.QUO_MFR || ''} | ${quote.QUO_MPN || ''} | ${quote.QUO_SUPP || ''}</div>
                            </div>
                        </div>
                    </div>`;
                });

                optionsHtml += '</div>';
                return optionsHtml;
            }

            // 绑定卡片事件
            function bindCardEvents(bomId, quotations, lowestPrice) {
                // 展开/折叠价格选项
                $(document).on('click', '.expand-prices-btn[data-bom-id="' + bomId + '"]', function() {
                    var $btn = $(this);
                    var $options = $('#price-options-' + bomId);

                    if ($options.hasClass('show')) {
                        $options.removeClass('show');
                        $btn.text('查看其他价格 (' + quotations.length + ')');
                    } else {
                        $options.addClass('show');
                        $btn.text('收起价格选项');
                    }
                });

                // 价格选择事件
                $(document).on('change', 'input[name="price_' + bomId + '"]', function() {
                    var $radio = $(this);
                    var selectedQuoId = $radio.val();
                    var selectedQuote = quotations.find(q => q.QUO_ID == selectedQuoId);
                    var $card = $('.bom-card[data-bom-id="' + bomId + '"]');

                    if (selectedQuote) {
                        // 更新卡片的选中报价ID
                        $card.attr('data-selected-quo-id', selectedQuoId);

                        // 更新当前价格显示
                        var selectedPrice = (selectedQuote.QUO_PRICE || 0).toFixed(2);
                        var isLowest = (selectedQuote.QUO_RMBPRICE || 0) === lowestPrice;

                        $card.find('.current-price').text('￥' + selectedPrice);

                        // 更新最低价标识
                        var $priceInfo = $card.find('.price-info');
                        $priceInfo.find('.lowest-price-badge').remove();
                        if (isLowest) {
                            $priceInfo.append('<span class="lowest-price-badge">最低价</span>');
                        }

                        // 更新详细信息
                        $card.find('.mfr-value').text(selectedQuote.QUO_MFR || '-');
                        $card.find('.mpn-value').text(selectedQuote.QUO_MPN || '-');
                        $card.find('.alter-mpn-value').text(selectedQuote.ALTER_MPN || '-');
                        $card.find('.supplier-value').text(selectedQuote.QUO_SUPP || '-');
                        $card.find('.currency-value').text(selectedQuote.QUO_CUR || '-');
                        $card.find('.usprice-value').text('$' + (selectedQuote.QUO_USPRICE || 0).toFixed(2));
                        $card.find('.spq-value').text(selectedQuote.SPQ || '-');
                        $card.find('.moq-value').text(selectedQuote.MOQ || '-');
                        $card.find('.lt-value').text(selectedQuote.LT || '-');
                        $card.find('.tool-cost-value').text(selectedQuote.TOOL_COST || '-');
                        $card.find('.tool-curr-value').text(selectedQuote.TOOL_CURR || '-');
                        $card.find('.date-value').text(selectedQuote.CREATE_DATE || '-');

                        // 更新价格选项的选中状态
                        $card.find('.price-option').removeClass('selected');
                        $radio.closest('.price-option').addClass('selected');

                        // 显示/隐藏备注提示
                        var $remarkRequired = $card.find('.remark-required');
                        if (!isLowest) {
                            $remarkRequired.show();
                        } else {
                            $remarkRequired.hide();
                        }
                    }
                });

                // 点击价格选项区域选择
                $(document).on('click', '.price-option[data-quo-id]', function(e) {
                    var $option = $(this);
                    var $radio = $option.find('input[type="radio"]');

                    if ($radio.attr('name') === 'price_' + bomId) {
                        if (!$radio.prop('checked')) {
                            $radio.prop('checked', true).trigger('change');
                        }
                        e.stopPropagation();
                    }
                });
            }

            // 提交审批（更新逻辑以适应新的卡片界面）
            $('#submitApproval').on('click', function () {
                var isValid = true;
                var errorMessage = '';
                var cardNumber = 0;

                $('.bom-card').each(function () {
                    cardNumber++;
                    var $card = $(this);
                    var bomId = $card.data('bom-id');
                    var $remarkInput = $card.find('.remark-input');
                    var selectedQuoId = $card.attr('data-selected-quo-id');
                    var lowestPrice = parseFloat($card.data('lowest-price'));

                    // 获取当前选中价格的RMB价格
                    var selectedRmbPrice = 0;
                    var $selectedRadio = $('input[name="price_' + bomId + '"]:checked');
                    if ($selectedRadio.length > 0) {
                        selectedRmbPrice = parseFloat($selectedRadio.attr('data-rmb-price')) || 0;
                    } else {
                        // 如果没有单选按钮（只有一个选项的情况），使用最低价
                        selectedRmbPrice = lowestPrice;
                    }

                    // 如果选择的价格不是最低价，需要填写备注
                    if (selectedRmbPrice > lowestPrice && !$remarkInput.val().trim()) {
                        isValid = false;
                        errorMessage += `第 ${cardNumber} 项选择非最低价，必须填写备注。\n`;
                    }
                });

                if (!isValid) {
                    layer.alert(errorMessage, { title: '提交验证失败' });
                    return;
                }

                var approvalData = [];
                $('.bom-card').each(function () {
                    var $card = $(this);
                    var bomId = $card.data('bom-id');
                    var quotationId = $card.attr('data-selected-quo-id');
                    var remark = $card.find('.remark-input').val().trim();

                    approvalData.push({
                        bomId: bomId,
                        quotationId: quotationId,
                        remark: remark
                    });
                });

                console.log(approvalData);

                $.ajax({
                    url: '../ashx/QuoteControl.ashx?action=SubmitApproval&RFQNo=' + RFQNo,
                    method: 'POST',
                    data: { approvalData: JSON.stringify(approvalData) },
                    success: function (response) {
                        layer.msg('审批申请成功');
                        //window.location = "index.html#/AssignFlow/FlowAssign.aspx";
                        navigateInIframe("../AssignFlow/FlowAssign.aspx", "流程单查询及签核");
                    },
                    error: function () {
                        layer.msg('审批申请失败');
                    }
                });
            });

            // 调试按钮事件
            $('#debugBtn').on('click', function () {
                console.log('=== 调试信息 ===');
                console.log('BOM卡片数量:', $('.bom-card').length);

                $('.bom-card').each(function (index) {
                    var $card = $(this);
                    var bomId = $card.data('bom-id');
                    console.log('卡片 ' + (index + 1) + ':', {
                        bomId: bomId,
                        selectedQuoId: $card.attr('data-selected-quo-id'),
                        lowestPrice: $card.data('lowest-price'),
                        hasExpandBtn: $card.find('.expand-prices-btn').length > 0,
                        priceOptionsCount: $card.find('.price-option').length,
                        radioButtonsCount: $card.find('input[type="radio"]').length,
                        priceOptionsVisible: $card.find('.price-options').hasClass('show')
                    });

                    $card.find('.price-option').each(function (optionIndex) {
                        var $option = $(this);
                        var $radio = $option.find('input[type="radio"]');
                        console.log('  价格选项 ' + (optionIndex + 1) + ':', {
                            quoId: $option.data('quo-id'),
                            rmbPrice: $option.data('rmb-price'),
                            isSelected: $option.hasClass('selected'),
                            radioChecked: $radio.prop('checked')
                        });
                    });
                });

                layer.alert('调试信息已输出到控制台，请按F12查看Console标签页');
            });

            // 初始化加载数据
            loadBOMData();
        });
    </script>
</body>
</html>